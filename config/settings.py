# config/settings.py
import os
from dotenv import load_dotenv

load_dotenv()  # 在应用启动时加载 .env 文件中的环境变量


class Settings:
    """应用配置类，从环境变量加载配置项"""
    
    # 七鱼API配置 (从环境变量加载，提供默认值用于开发或非敏感配置)
    QIYU_APP_KEY: str = os.getenv("QIYU_APP_KEY", "your_qiyu_app_key_default")
    QIYU_APP_SECRET: str = os.getenv("QIYU_APP_SECRET", "your_qiyu_app_secret_default")
    QIYU_BASE_URL: str = os.getenv("QIYU_BASE_URL", "https://qiyukf.com")

    # 密码重置API配置
    PASSWORD_RESET_USERNAME: str = os.getenv("PASSWORD_RESET_USERNAME", "your_reset_username_default")
    PASSWORD_RESET_PASSWORD: str = os.getenv("PASSWORD_RESET_PASSWORD", "your_reset_password_default")
    PASSWORD_RESET_BASE_URL: str = os.getenv("PASSWORD_RESET_BASE_URL", "https://op-api.preternatural.cn")

    # 数据库配置
    DATABASE_PATH: str = os.getenv("DATABASE_PATH", "ticket_records.db")

    # 工单组配置
    SOURCE_TICKET_GROUP_ID: str = os.getenv("SOURCE_TICKET_GROUP_ID", "485590143")
    TARGET_TICKET_GROUP_ID: str = os.getenv("TARGET_TICKET_GROUP_ID", "*********")

    # 处理配置
    TICKET_FETCH_COUNT: int = int(os.getenv("TICKET_FETCH_COUNT", "50"))
    CHANNEL_TO_FILTER: str = os.getenv("CHANNEL_TO_FILTER", "233_first")
    TRANSFER_COMMENT: str = os.getenv("TRANSFER_COMMENT", "已分拣：条件匹配成功，转交处理")
    STAFF_ID: int = int(os.getenv("STAFF_ID", "6481806"))
    FILTER_ID: int = int(os.getenv("FILTER_ID", "********"))

    # 账号爬虫配置
    ACCOUNT_SCRAPER_BASE_URL: str = os.getenv("ACCOUNT_SCRAPER_BASE_URL", "https://your-scraper-api.com")


# 创建一个全局的Settings实例，方便在应用中引用
settings = Settings()
