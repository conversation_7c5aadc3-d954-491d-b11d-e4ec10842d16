# clients/qiyu_api_client.py
import hashlib
import json
import time
import requests
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger('clients.qiyu_api_client')


class QiyuAPIClient:
    """七鱼API客户端"""
    
    def __init__(self, app_key: str, app_secret: str, base_url: str):
        self.app_key = app_key
        self.app_secret = app_secret
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json;charset=utf-8',
            'User-Agent': 'QiyuAPI-Python-Client/1.0'
        })

    def _generate_signature(self, timestamp: str, nonce: str) -> str:
        """生成API签名"""
        sign_str = f"{self.app_key}{nonce}{timestamp}{self.app_secret}"
        return hashlib.sha1(sign_str.encode('utf-8')).hexdigest()

    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送API请求"""
        timestamp = str(int(time.time()))
        nonce = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp, nonce)
        
        headers = {
            'AppKey': self.app_key,
            'Nonce': nonce,
            'CurTime': timestamp,
            'CheckSum': signature
        }
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.post(url, json=data, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 200:
                return result.get('result')
            else:
                logger.error(f"API请求失败: {result.get('message', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None

    def get_filter_tickets(self, staff_id: int, filter_id: int, limit: int, offset: int = 0) -> Optional[Dict[str, Any]]:
        """获取筛选器工单"""
        data = {
            'staffId': staff_id,
            'filterId': filter_id,
            'limit': limit,
            'offset': offset
        }
        
        logger.info(f"获取筛选器工单: staffId={staff_id}, filterId={filter_id}, limit={limit}")
        return self._make_request('/openapi/ticket/list', data)

    def get_ticket_detail(self, ticket_id: int) -> Optional[Dict[str, Any]]:
        """获取工单详情"""
        data = {'ticketId': ticket_id}
        
        logger.info(f"获取工单详情: ticketId={ticket_id}")
        return self._make_request('/openapi/ticket/detail', data)

    def claim_ticket(self, ticket_id: int, staff_id: int) -> bool:
        """申领工单"""
        data = {
            'ticketId': ticket_id,
            'staffId': staff_id
        }
        
        logger.info(f"申领工单: ticketId={ticket_id}, staffId={staff_id}")
        result = self._make_request('/openapi/ticket/claim', data)
        return result is not None

    def finish_ticket(self, ticket_id: int, staff_id: int, comment: str) -> bool:
        """完结工单"""
        data = {
            'ticketId': ticket_id,
            'staffId': staff_id,
            'comment': comment
        }
        
        logger.info(f"完结工单: ticketId={ticket_id}, staffId={staff_id}")
        result = self._make_request('/openapi/ticket/finish', data)
        return result is not None

    def transfer_ticket(self, ticket_id: int, staff_id: int, target_group_id: str, comment: str) -> bool:
        """转交工单"""
        data = {
            'ticketId': ticket_id,
            'staffId': staff_id,
            'targetGroupId': target_group_id,
            'comment': comment
        }
        
        logger.info(f"转交工单: ticketId={ticket_id}, targetGroupId={target_group_id}")
        result = self._make_request('/openapi/ticket/transfer', data)
        return result is not None
