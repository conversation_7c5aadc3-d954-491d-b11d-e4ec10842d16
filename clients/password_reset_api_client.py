# clients/password_reset_api_client.py
import requests
import json
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger('clients.password_reset_api_client')


class PasswordResetClient:
    """密码重置API客户端"""
    
    def __init__(self, username: str, password: str, base_url: str):
        self.username = username
        self.password = password
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'PasswordReset-Python-Client/1.0'
        })

    def login_and_get_token(self) -> Optional[str]:
        """登录并获取token"""
        login_url = f"{self.base_url}/common/login?locate=0"
        
        login_data = {
            'username': self.username,
            'password': self.password
        }
        
        try:
            logger.info("正在登录密码重置系统...")
            response = self.session.post(login_url, json=login_data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 200:
                self.token = result.get('data', {}).get('token')
                if self.token:
                    self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                    logger.info("登录成功，已获取token")
                    return self.token
                else:
                    logger.error("登录响应中未找到token")
                    return None
            else:
                logger.error(f"登录失败: {result.get('message', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"登录请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"登录响应JSON解析失败: {e}")
            return None

    def get_player_info(self, player_id: str) -> Optional[Dict[str, Any]]:
        """获取玩家信息"""
        if not self.token:
            logger.error("未登录，无法获取玩家信息")
            return None
            
        info_url = f"{self.base_url}/api/party/op/player/info?locate=6"
        
        params = {'playerId': player_id}
        
        try:
            logger.info(f"获取玩家信息: playerId={player_id}")
            response = self.session.get(info_url, params=params, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 200:
                return result.get('data')
            else:
                logger.error(f"获取玩家信息失败: {result.get('message', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"获取玩家信息请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"玩家信息响应JSON解析失败: {e}")
            return None

    def reset_account_password(self, account_id: str) -> Optional[str]:
        """重置账号密码"""
        if not self.token:
            logger.error("未登录，无法重置密码")
            return None
            
        reset_url = f"{self.base_url}/api/party/op/player/resetPassword"
        
        reset_data = {
            'accountId': account_id
        }
        
        try:
            logger.info(f"重置账号密码: accountId={account_id}")
            response = self.session.post(reset_url, json=reset_data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 200:
                new_password = result.get('data', {}).get('newPassword')
                if new_password:
                    logger.info(f"密码重置成功: accountId={account_id}")
                    return new_password
                else:
                    logger.error("密码重置响应中未找到新密码")
                    return None
            else:
                logger.error(f"密码重置失败: {result.get('message', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"密码重置请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"密码重置响应JSON解析失败: {e}")
            return None
