# services/account_scraper_service.py
import logging
from typing import Dict, Any, Optional
from clients.account_scraper_api_client import AccountScraperClient
from utils.exceptions import AccountScraperError

logger = logging.getLogger('services.account_scraper_service')


class AccountScraperService:
    """账号信息爬取业务服务类"""
    
    def __init__(self, api_client: AccountScraperClient):
        self.api_client = api_client

    def get_account_details(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取账号详细信息"""
        try:
            logger.info(f"开始获取账号 {account_id} 的详细信息")
            
            scraped_data = self.api_client.scrape_account_info(account_id)
            if not scraped_data:
                logger.warning(f"未能获取账号 {account_id} 的信息")
                return None
            
            # 验证返回数据的完整性
            required_fields = ['account_id', 'real_name', 'last_login_address', 'phone_bound', 'channel_user']
            missing_fields = [field for field in required_fields if field not in scraped_data]
            
            if missing_fields:
                logger.warning(f"账号 {account_id} 信息缺少字段: {missing_fields}")
                # 为缺少的字段设置默认值
                for field in missing_fields:
                    if field == 'phone_bound':
                        scraped_data[field] = False
                    elif field == 'channel_user':
                        scraped_data[field] = False
                    else:
                        scraped_data[field] = ""
            
            logger.info(f"成功获取账号 {account_id} 的详细信息")
            return scraped_data
            
        except Exception as e:
            logger.error(f"获取账号详细信息异常: {e}")
            raise AccountScraperError(f"获取账号详细信息失败: {e}")

    def validate_account_exists(self, account_id: str) -> bool:
        """验证账号是否存在"""
        try:
            account_info = self.get_account_details(account_id)
            return account_info is not None
            
        except AccountScraperError:
            return False

    def get_account_basic_info(self, account_id: str) -> Optional[Dict[str, str]]:
        """获取账号基本信息（仅姓名和身份证）"""
        try:
            full_info = self.get_account_details(account_id)
            if not full_info:
                return None
            
            return {
                'account_id': full_info.get('account_id', ''),
                'real_name': full_info.get('real_name', ''),
                'id_card': full_info.get('id_card', '')
            }
            
        except Exception as e:
            logger.error(f"获取账号基本信息异常: {e}")
            raise AccountScraperError(f"获取账号基本信息失败: {e}")

    def check_phone_binding(self, account_id: str) -> bool:
        """检查账号是否绑定手机"""
        try:
            account_info = self.get_account_details(account_id)
            if not account_info:
                return False
            
            return account_info.get('phone_bound', False)
            
        except Exception as e:
            logger.error(f"检查手机绑定状态异常: {e}")
            return False

    def check_channel_user(self, account_id: str) -> tuple[bool, str]:
        """检查是否为渠道用户，返回(是否渠道用户, 渠道名称)"""
        try:
            account_info = self.get_account_details(account_id)
            if not account_info:
                return False, ""
            
            is_channel = account_info.get('channel_user', False)
            channel_name = account_info.get('channel_name', '')
            
            return is_channel, channel_name
            
        except Exception as e:
            logger.error(f"检查渠道用户状态异常: {e}")
            return False, ""

    def get_last_login_address(self, account_id: str) -> Optional[str]:
        """获取最后登录地址"""
        try:
            account_info = self.get_account_details(account_id)
            if not account_info:
                return None
            
            return account_info.get('last_login_address')
            
        except Exception as e:
            logger.error(f"获取最后登录地址异常: {e}")
            return None
