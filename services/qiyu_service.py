# services/qiyu_service.py
import logging
from typing import Dict, List, Any, Optional
from clients.qiyu_api_client import QiyuAPIClient
from utils.exceptions import QiyuAPIError
from utils.constants import TICKET_STATUS_NAMES

logger = logging.getLogger('services.qiyu_service')


class QiyuService:
    """七鱼业务服务类"""
    
    def __init__(self, api_client: QiyuAPIClient):
        self.api_client = api_client
        self.status_names = TICKET_STATUS_NAMES

    def get_tickets_by_filter(self, staff_id: int, filter_id: int, limit: int, offset: int = 0) -> List[Dict]:
        """获取筛选器工单列表"""
        try:
            result = self.api_client.get_filter_tickets(staff_id, filter_id, limit, offset)
            if result is None:
                raise QiyuAPIError("获取工单列表失败")
            
            tickets = result.get('tickets', [])
            logger.info(f"成功获取 {len(tickets)} 个工单")
            return tickets
            
        except Exception as e:
            logger.error(f"获取工单列表异常: {e}")
            raise QiyuAPIError(f"获取工单列表失败: {e}")

    def get_ticket_details(self, ticket_id: int) -> Dict[str, Any]:
        """获取工单详情并标准化自定义字段"""
        try:
            result = self.api_client.get_ticket_detail(ticket_id)
            if result is None:
                raise QiyuAPIError(f"获取工单 {ticket_id} 详情失败")
            
            # 标准化自定义字段
            custom_fields = {}
            if 'custom' in result and isinstance(result['custom'], list):
                for field in result['custom']:
                    if isinstance(field, dict) and 'name' in field and 'value' in field:
                        custom_fields[field['name']] = field['value']
            
            result['custom_fields'] = custom_fields
            
            logger.info(f"成功获取工单 {ticket_id} 详情")
            return result
            
        except Exception as e:
            logger.error(f"获取工单详情异常: {e}")
            raise QiyuAPIError(f"获取工单详情失败: {e}")

    def claim_and_close_ticket(self, ticket_id: int, staff_id: int, comment: str) -> bool:
        """申领并完结工单"""
        try:
            # 先获取工单状态
            detail = self.get_ticket_details(ticket_id)
            ticket_status = detail.get('status')
            
            logger.info(f"工单 {ticket_id} 当前状态: {self.status_names.get(ticket_status, '未知')}({ticket_status})")
            
            # 如果是待申领状态，先申领
            if ticket_status == 5:  # 待申领
                if not self.api_client.claim_ticket(ticket_id, staff_id):
                    raise QiyuAPIError(f"申领工单 {ticket_id} 失败")
                logger.info(f"成功申领工单 {ticket_id}")
            
            # 完结工单
            if not self.api_client.finish_ticket(ticket_id, staff_id, comment):
                raise QiyuAPIError(f"完结工单 {ticket_id} 失败")
            
            logger.info(f"成功完结工单 {ticket_id}")
            return True
            
        except Exception as e:
            logger.error(f"申领并完结工单异常: {e}")
            raise QiyuAPIError(f"申领并完结工单失败: {e}")

    def transfer_ticket(self, ticket_id: int, staff_id: int, target_group_id: str, comment: str) -> bool:
        """转交工单"""
        try:
            # 先获取工单状态
            detail = self.get_ticket_details(ticket_id)
            ticket_status = detail.get('status')
            
            logger.info(f"工单 {ticket_id} 当前状态: {self.status_names.get(ticket_status, '未知')}({ticket_status})")
            
            # 如果是待申领状态，先申领
            if ticket_status == 5:  # 待申领
                if not self.api_client.claim_ticket(ticket_id, staff_id):
                    raise QiyuAPIError(f"申领工单 {ticket_id} 失败")
                logger.info(f"成功申领工单 {ticket_id}")
            
            # 转交工单
            if not self.api_client.transfer_ticket(ticket_id, staff_id, target_group_id, comment):
                raise QiyuAPIError(f"转交工单 {ticket_id} 失败")
            
            logger.info(f"成功转交工单 {ticket_id} 到组 {target_group_id}")
            return True
            
        except Exception as e:
            logger.error(f"转交工单异常: {e}")
            raise QiyuAPIError(f"转交工单失败: {e}")

    def get_ticket_status_name(self, status: int) -> str:
        """获取工单状态名称"""
        return self.status_names.get(status, f'未知状态({status})')
