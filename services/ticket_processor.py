# services/ticket_processor.py
import logging
from typing import Dict, Any, List, Optional

from config.settings import settings
from data.database import TicketDatabase
from services.qiyu_service import QiyuService
from services.password_reset_service import PasswordResetService
from services.account_scraper_service import AccountScraperService
from clients.qiyu_api_client import <PERSON><PERSON><PERSON><PERSON><PERSON>
from clients.password_reset_api_client import PasswordResetClient
from clients.account_scraper_api_client import Account<PERSON><PERSON>raper<PERSON>lient
from utils.address_matcher import match_addresses
from utils.exceptions import (
    TicketProcessingError, DataValidationError, QiyuAPIError, 
    PasswordResetAPIError, AccountScraperError
)
from utils.constants import RESPONSE_TEMPLATES

logger = logging.getLogger('services.ticket_processor')


class TicketProcessor:
    """工单处理器 - 核心业务流程协调者"""
    
    def __init__(self):
        # 初始化所有依赖的客户端
        self.qiyu_api_client = QiyuAPIClient(
            app_key=settings.QIYU_APP_KEY,
            app_secret=settings.QIYU_APP_SECRET,
            base_url=settings.QIYU_BASE_URL
        )
        self.password_reset_api_client = PasswordResetClient(
            username=settings.PASSWORD_RESET_USERNAME,
            password=settings.PASSWORD_RESET_PASSWORD,
            base_url=settings.PASSWORD_RESET_BASE_URL
        )
        self.account_scraper_api_client = AccountScraperClient(
            base_url=settings.ACCOUNT_SCRAPER_BASE_URL
        )

        # 初始化服务层
        self.qiyu_service = QiyuService(self.qiyu_api_client)
        self.password_reset_service = PasswordResetService(self.password_reset_api_client)
        self.account_scraper_service = AccountScraperService(self.account_scraper_api_client)
        self.database = TicketDatabase(settings.DATABASE_PATH)

    def process_tickets_batch(self) -> Dict[str, int]:
        """
        主处理流程：获取工单并逐一处理。
        返回处理统计信息
        """
        logger.info("开始执行工单自动处理流程...")
        success_count = 0
        error_count = 0
        processed_count = 0
        skipped_count = 0

        try:
            tickets = self.qiyu_service.get_tickets_by_filter(
                staff_id=settings.STAFF_ID,
                filter_id=settings.FILTER_ID,
                limit=settings.TICKET_FETCH_COUNT
            )

            if not tickets:
                logger.info("未获取到需要处理的工单。")
                return {
                    'processed': 0,
                    'success': 0,
                    'error': 0,
                    'skipped': 0
                }

            for ticket in tickets:
                processed_count += 1
                ticket_id = ticket.get('ticketId')
                logger.info(f"\n--- 开始处理工单 {ticket_id} ({processed_count}/{len(tickets)}) ---")
                
                try:
                    result = self._process_single_ticket(ticket_id)
                    if result == 'success':
                        success_count += 1
                    elif result == 'skipped':
                        skipped_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    logger.error(f"处理工单 {ticket_id} 时发生未知异常: {e}", exc_info=True)
                    error_count += 1
                    continue  # 继续处理下一个工单

        except Exception as e:
            logger.critical(f"主处理流程发生严重异常: {e}", exc_info=True)
            raise  # 重新抛出，可能需要外部处理或停止服务

        finally:
            self._log_processing_summary(processed_count, success_count, error_count, skipped_count)

        return {
            'processed': processed_count,
            'success': success_count,
            'error': error_count,
            'skipped': skipped_count
        }

    def _process_single_ticket(self, ticket_id: int) -> str:
        """
        处理单个工单
        返回: 'success', 'error', 'skipped'
        """
        try:
            # 1. 获取工单详情并判断状态
            detail = self.qiyu_service.get_ticket_details(ticket_id)
            ticket_status = detail.get('status')
            
            if ticket_status not in [1, 5]:  # 已提交(1) 或 待申领(5)
                status_name = self.qiyu_service.get_ticket_status_name(ticket_status)
                logger.info(f"工单 {ticket_id} 状态为 {status_name}，跳过处理。")
                return 'skipped'

            # 2. 提取工单信息 (从 custom_fields 中获取)
            custom_fields = detail.get('custom_fields', {})
            account_id = custom_fields.get('工号')
            ticket_real_name = custom_fields.get('姓名')
            ticket_id_card = custom_fields.get('身份证')
            ticket_last_login_address = custom_fields.get('最后登录IP属地')

            if not account_id:
                logger.warning(f"工单 {ticket_id} 缺少工号信息，完结工单。")
                self.qiyu_service.claim_and_close_ticket(
                    ticket_id, settings.STAFF_ID, RESPONSE_TEMPLATES['ACCOUNT_NOT_FOUND']
                )
                return 'success'

            # 3. 爬取账号信息
            scraped_data = self.account_scraper_service.get_account_details(account_id)
            if not scraped_data:
                logger.warning(f"未找到工号 {account_id} 的账号信息，完结工单。")
                self.qiyu_service.claim_and_close_ticket(
                    ticket_id, settings.STAFF_ID, RESPONSE_TEMPLATES['ACCOUNT_NOT_FOUND']
                )
                return 'success'

            # 4. 业务逻辑判断
            return self._apply_business_rules(
                ticket_id, account_id, scraped_data, 
                ticket_real_name, ticket_id_card, ticket_last_login_address
            )

        except (QiyuAPIError, PasswordResetAPIError, AccountScraperError, DataValidationError) as e:
            logger.error(f"处理工单 {ticket_id} 业务逻辑错误: {e}")
            return 'error'

    def _apply_business_rules(self, ticket_id: int, account_id: str, scraped_data: Dict[str, Any],
                            ticket_real_name: str, ticket_id_card: str, 
                            ticket_last_login_address: str) -> str:
        """应用业务规则进行判断和处理"""
        
        # 检查手机绑定
        if scraped_data.get('phone_bound', False):
            self.qiyu_service.claim_and_close_ticket(
                ticket_id, settings.STAFF_ID, RESPONSE_TEMPLATES['PHONE_EXISTS']
            )
            return 'success'

        # 检查渠道用户
        if (scraped_data.get('channel_user', False) and 
            scraped_data.get('channel_name') == settings.CHANNEL_TO_FILTER):
            self.qiyu_service.claim_and_close_ticket(
                ticket_id, settings.STAFF_ID, RESPONSE_TEMPLATES['IS_CHANNEL_USER']
            )
            return 'success'

        # 实名匹配
        if not self._match_identity(scraped_data, ticket_real_name, ticket_id_card):
            self.qiyu_service.claim_and_close_ticket(
                ticket_id, settings.STAFF_ID, RESPONSE_TEMPLATES['NAME_MISMATCH']
            )
            return 'success'

        # 地址匹配
        scraped_address = scraped_data.get('last_login_address')
        if not match_addresses(ticket_last_login_address, scraped_address):
            self.qiyu_service.claim_and_close_ticket(
                ticket_id, settings.STAFF_ID, RESPONSE_TEMPLATES['ADDRESS_MISMATCH_COMMENT']
            )
            return 'success'

        # 检查是否已处理
        if self.database.check_account_processed(account_id):
            self.qiyu_service.claim_and_close_ticket(
                ticket_id, settings.STAFF_ID, RESPONSE_TEMPLATES['ALREADY_PROCESSED_COMMENT']
            )
            return 'success'

        # 执行密码重置流程
        return self._execute_password_reset(
            ticket_id, account_id, scraped_data, 
            ticket_real_name, ticket_id_card, scraped_address
        )

    def _execute_password_reset(self, ticket_id: int, account_id: str, scraped_data: Dict[str, Any],
                              ticket_real_name: str, ticket_id_card: str, scraped_address: str) -> str:
        """执行密码重置流程"""
        try:
            # 执行密码重置
            new_password = self.password_reset_service.reset_account_password_flow(account_id)
            if not new_password:
                logger.error(f"工单 {ticket_id} 工号 {account_id} 密码重置失败，跳过记录和完结。")
                return 'error'

            # 记录数据库
            self.database.add_record(
                account_id=account_id,
                real_name=ticket_real_name,
                id_card=ticket_id_card,
                last_login_address=scraped_address,
                reset_password=new_password,
                ticket_id=ticket_id,
                staff_id=settings.STAFF_ID
            )

            # 完结工单并回复新密码
            success_comment = RESPONSE_TEMPLATES['PASSWORD_RESET_SUCCESS_TEMPLATE'].format(
                password=new_password
            )
            self.qiyu_service.claim_and_close_ticket(ticket_id, settings.STAFF_ID, success_comment)
            
            logger.info(f"工单 {ticket_id} 处理完成，密码已重置并回复。")
            return 'success'

        except Exception as e:
            logger.error(f"密码重置流程异常: {e}")
            return 'error'

    def _match_identity(self, scraped_data: Dict[str, Any], ticket_real_name: str, ticket_id_card: str) -> bool:
        """身份匹配逻辑"""
        scraped_real_name = scraped_data.get('real_name', '')
        scraped_id_card = scraped_data.get('id_card', '')
        
        # 姓名匹配
        name_match = scraped_real_name == ticket_real_name
        
        # 身份证匹配（可以是部分匹配）
        id_card_match = False
        if scraped_id_card and ticket_id_card:
            # 简单的身份证匹配逻辑，可以根据需要调整
            id_card_match = scraped_id_card == ticket_id_card
        
        return name_match and id_card_match

    def _log_processing_summary(self, processed: int, success: int, error: int, skipped: int) -> None:
        """记录处理摘要"""
        logger.info("\n" + "=" * 60)
        logger.info("处理完成统计:")
        logger.info(f"总处理工单数: {processed}")
        logger.info(f"成功处理数: {success}")
        logger.info(f"跳过处理数: {skipped}")
        logger.info(f"错误数: {error}")
        logger.info("=" * 60)
