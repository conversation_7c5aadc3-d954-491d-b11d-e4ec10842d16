# services/password_reset_service.py
import logging
from typing import Optional
from clients.password_reset_api_client import PasswordResetClient
from utils.exceptions import PasswordResetAPIError

logger = logging.getLogger('services.password_reset_service')


class PasswordResetService:
    """密码重置业务服务类"""
    
    def __init__(self, api_client: PasswordResetClient):
        self.api_client = api_client
        self._logged_in = False

    def _ensure_logged_in(self) -> bool:
        """确保已登录"""
        if not self._logged_in:
            token = self.api_client.login_and_get_token()
            if token:
                self._logged_in = True
                return True
            else:
                return False
        return True

    def reset_account_password_flow(self, account_id: str) -> Optional[str]:
        """执行完整的密码重置流程"""
        try:
            # 确保已登录
            if not self._ensure_logged_in():
                raise PasswordResetAPIError("登录密码重置系统失败")
            
            logger.info(f"开始为账号 {account_id} 执行密码重置流程")
            
            # 1. 获取玩家信息（验证账号存在）
            player_info = self.api_client.get_player_info(account_id)
            if not player_info:
                logger.warning(f"账号 {account_id} 不存在或获取信息失败")
                return None
            
            logger.info(f"账号 {account_id} 信息验证成功")
            
            # 2. 执行密码重置
            new_password = self.api_client.reset_account_password(account_id)
            if not new_password:
                raise PasswordResetAPIError(f"账号 {account_id} 密码重置失败")
            
            logger.info(f"账号 {account_id} 密码重置成功")
            return new_password
            
        except Exception as e:
            logger.error(f"密码重置流程异常: {e}")
            if isinstance(e, PasswordResetAPIError):
                raise
            else:
                raise PasswordResetAPIError(f"密码重置流程失败: {e}")

    def get_player_info(self, account_id: str) -> Optional[dict]:
        """获取玩家信息"""
        try:
            if not self._ensure_logged_in():
                raise PasswordResetAPIError("登录密码重置系统失败")
            
            return self.api_client.get_player_info(account_id)
            
        except Exception as e:
            logger.error(f"获取玩家信息异常: {e}")
            if isinstance(e, PasswordResetAPIError):
                raise
            else:
                raise PasswordResetAPIError(f"获取玩家信息失败: {e}")

    def reset_password_only(self, account_id: str) -> Optional[str]:
        """仅执行密码重置（不验证玩家信息）"""
        try:
            if not self._ensure_logged_in():
                raise PasswordResetAPIError("登录密码重置系统失败")
            
            new_password = self.api_client.reset_account_password(account_id)
            if not new_password:
                raise PasswordResetAPIError(f"账号 {account_id} 密码重置失败")
            
            return new_password
            
        except Exception as e:
            logger.error(f"密码重置异常: {e}")
            if isinstance(e, PasswordResetAPIError):
                raise
            else:
                raise PasswordResetAPIError(f"密码重置失败: {e}")

    def logout(self) -> None:
        """登出"""
        self._logged_in = False
        self.api_client.token = None
        if 'Authorization' in self.api_client.session.headers:
            del self.api_client.session.headers['Authorization']
        logger.info("已登出密码重置系统")
