[tool.poetry]
name = "ticket-automation-system"
version = "0.1.0"
description = "工单处理自动化系统"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
requests = "^2.31.0"
cpca = "^1.0.9"
python-dotenv = "^1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-mock = "^3.11.1"
ruff = "^0.1.0"
mypy = "^1.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 120
select = ["E", "F", "W", "I", "N", "D"]
ignore = ["D100", "D104", "D107"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true
