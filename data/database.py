# data/database.py
import sqlite3
import logging
import datetime
import os
from typing import Optional, List, Dict, Any

logger = logging.getLogger('data.database')


class TicketDatabase:
    """工单数据库管理类"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()

    def init_database(self) -> None:
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建工单处理记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS ticket_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        account_id TEXT NOT NULL,
                        real_name TEXT,
                        id_card TEXT,
                        last_login_address TEXT,
                        reset_password TEXT,
                        ticket_id INTEGER,
                        staff_id INTEGER,
                        processed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(account_id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_account_id ON ticket_records(account_id)
                ''')
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_processed_time ON ticket_records(processed_time)
                ''')
                
                conn.commit()
                logger.info(f"数据库初始化完成: {self.db_path}")
                
        except sqlite3.Error as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    def add_record(self, account_id: str, real_name: str, id_card: str, 
                   last_login_address: str, reset_password: str, 
                   ticket_id: int, staff_id: int) -> bool:
        """添加处理记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO ticket_records 
                    (account_id, real_name, id_card, last_login_address, 
                     reset_password, ticket_id, staff_id, processed_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (account_id, real_name, id_card, last_login_address,
                      reset_password, ticket_id, staff_id, datetime.datetime.now()))
                
                conn.commit()
                logger.info(f"成功添加处理记录: account_id={account_id}, ticket_id={ticket_id}")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"添加处理记录失败: {e}")
            return False

    def check_account_processed(self, account_id: str) -> bool:
        """检查账号是否已处理过"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT COUNT(*) FROM ticket_records WHERE account_id = ?
                ''', (account_id,))
                
                count = cursor.fetchone()[0]
                return count > 0
                
        except sqlite3.Error as e:
            logger.error(f"检查账号处理状态失败: {e}")
            return False

    def get_records_by_account(self, account_id: str) -> List[Dict[str, Any]]:
        """根据账号ID获取处理记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM ticket_records WHERE account_id = ?
                    ORDER BY processed_time DESC
                ''', (account_id,))
                
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            logger.error(f"查询账号记录失败: {e}")
            return []

    def get_recent_records(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取最近的处理记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM ticket_records 
                    ORDER BY processed_time DESC 
                    LIMIT ?
                ''', (limit,))
                
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            logger.error(f"查询最近记录失败: {e}")
            return []

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总记录数
                cursor.execute('SELECT COUNT(*) FROM ticket_records')
                total_count = cursor.fetchone()[0]
                
                # 今日处理数
                today = datetime.date.today()
                cursor.execute('''
                    SELECT COUNT(*) FROM ticket_records 
                    WHERE DATE(processed_time) = ?
                ''', (today,))
                today_count = cursor.fetchone()[0]
                
                # 本周处理数
                week_start = today - datetime.timedelta(days=today.weekday())
                cursor.execute('''
                    SELECT COUNT(*) FROM ticket_records 
                    WHERE DATE(processed_time) >= ?
                ''', (week_start,))
                week_count = cursor.fetchone()[0]
                
                # 数据库文件大小
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                
                return {
                    'total_count': total_count,
                    'today_count': today_count,
                    'week_count': week_count,
                    'database_path': self.db_path,
                    'database_size_bytes': db_size
                }
                
        except sqlite3.Error as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def export_to_csv(self, output_file: str) -> bool:
        """导出数据到CSV文件"""
        try:
            import csv
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM ticket_records ORDER BY processed_time DESC')
                rows = cursor.fetchall()
                
                if not rows:
                    logger.warning("没有数据可导出")
                    return False
                
                with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = rows[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    writer.writeheader()
                    for row in rows:
                        writer.writerow(dict(row))
                
                logger.info(f"数据导出成功: {output_file}")
                return True
                
        except Exception as e:
            logger.error(f"数据导出失败: {e}")
            return False
