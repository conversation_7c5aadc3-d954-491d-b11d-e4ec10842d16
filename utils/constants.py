# utils/constants.py

# 自动回复话术模板
RESPONSE_TEMPLATES = {
    'ACCOUNT_NOT_FOUND': "您好工友，这边无法查询到您填写的工号，请您填写正确的游戏账号（工号）后再次提交您账号找回工单，我们会尽快为您处理的哦~",
    'PHONE_EXISTS': "您好工友，后台核查您的账号已有绑定手机，您可以在游戏内点击切换账号-添加用户-忘记密码直接找回您的账号密码哦~",
    'IS_CHANNEL_USER': "您好，您的账号为233渠道服账号，您可以在233乐园上下载超自然行动组，即可找回您的账号哦~若需修改密码，请先更改渠道账号密码，再登录游戏哦～",
    'NAME_MISMATCH': "您好工友，您填写的实名信息与账号实名信息不匹配，麻烦您填写正确的实名信息后再次提交您账号找回工单，我们会尽快为您处理的哦~",
    'ADDRESS_MISMATCH_COMMENT': "您好工友，您填写的最后登录地址与账号实际登录地址不一致，麻烦您填写正确的登录地址后再次提交您账号找回工单，我们会尽快为您处理的哦~",
    'PASSWORD_RESET_SUCCESS_TEMPLATE': "您好工友，经核实您的信息无误，已为您重置密码。新密码为：{password}，请妥善保管并及时修改，感谢您的支持。",
    'ALREADY_PROCESSED_COMMENT': "您好工友，您的账号已经处理过找回申请，如有其他问题请联系客服，感谢您的支持。"
}

# 工单状态映射
TICKET_STATUS_NAMES = {
    1: '已提交',
    5: '待申领',
    10: '处理中',
    20: '已完结',
    25: '已驳回',
    50: '已挂起'
}
