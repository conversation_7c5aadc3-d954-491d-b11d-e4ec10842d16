# utils/logger.py
import logging
import datetime
import os


def setup_logging() -> None:
    """
    设置全局日志配置。
    日志将输出到文件和控制台，并确保不会重复添加handler。
    """
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'ticket_processor_{datetime.datetime.now().strftime("%Y%m%d")}.log')

    # 获取根logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)  # 设置默认级别

    # 避免重复添加handler
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 文件Handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)

    # 控制台Handler
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(formatter)
    root_logger.addHandler(stream_handler)

    logger = logging.getLogger(__name__)
    logger.info("日志系统初始化完成。")


# 注意：在其他模块中，只需 `import logging; logger = logging.getLogger(__name__)` 即可获取并使用logger
