# utils/address_matcher.py
import re
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger('utils.address_matcher')

# 省份映射表
PROVINCE_MAPPING = {
    '北京': '北京市',
    '天津': '天津市',
    '上海': '上海市',
    '重庆': '重庆市',
    '河北': '河北省',
    '山西': '山西省',
    '辽宁': '辽宁省',
    '吉林': '吉林省',
    '黑龙江': '黑龙江省',
    '江苏': '江苏省',
    '浙江': '浙江省',
    '安徽': '安徽省',
    '福建': '福建省',
    '江西': '江西省',
    '山东': '山东省',
    '河南': '河南省',
    '湖北': '湖北省',
    '湖南': '湖南省',
    '广东': '广东省',
    '海南': '海南省',
    '四川': '四川省',
    '贵州': '贵州省',
    '云南': '云南省',
    '陕西': '陕西省',
    '甘肃': '甘肃省',
    '青海': '青海省',
    '台湾': '台湾省',
    '内蒙古': '内蒙古自治区',
    '广西': '广西壮族自治区',
    '西藏': '西藏自治区',
    '宁夏': '宁夏回族自治区',
    '新疆': '新疆维吾尔自治区',
    '香港': '香港特别行政区',
    '澳门': '澳门特别行政区'
}


def extract_address_info(address: str) -> Dict[str, Any]:
    """
    提取地址信息，包括省份、城市等
    """
    if not address:
        return {}
    
    try:
        # 这里可以使用cpca库进行地址解析
        # import cpca
        # result = cpca.transform([address])
        # return result[0] if result else {}
        
        # 简化版本：使用正则表达式提取
        result = {}
        
        # 提取省份
        for province_short, province_full in PROVINCE_MAPPING.items():
            if province_short in address or province_full in address:
                result['省'] = province_full
                break
        
        # 提取城市（简化处理）
        city_pattern = r'([^省市区县]+市)'
        city_match = re.search(city_pattern, address)
        if city_match:
            result['市'] = city_match.group(1)
        
        return result
    except Exception as e:
        logger.error(f"地址解析失败: {address}, 错误: {e}")
        return {}


def extract_keywords(address: str) -> List[str]:
    """
    从地址中提取关键词
    """
    if not address:
        return []
    
    # 移除常见的无意义词汇
    stop_words = ['省', '市', '区', '县', '镇', '乡', '街道', '路', '号', '栋', '单元', '室']
    
    # 使用正则表达式提取中文词汇
    keywords = re.findall(r'[\u4e00-\u9fff]+', address)
    
    # 过滤停用词和短词
    filtered_keywords = []
    for keyword in keywords:
        if len(keyword) >= 2 and keyword not in stop_words:
            filtered_keywords.append(keyword)
    
    return filtered_keywords


def match_addresses(address1: str, address2: str) -> bool:
    """
    匹配两个地址是否相似
    """
    if not address1 or not address2:
        return False
    
    # 提取地址信息
    info1 = extract_address_info(address1)
    info2 = extract_address_info(address2)
    
    # 如果都有省份信息，比较省份
    if '省' in info1 and '省' in info2:
        if info1['省'] == info2['省']:
            # 省份相同，进一步比较城市
            if '市' in info1 and '市' in info2:
                return info1['市'] == info2['市']
            return True
        return False
    
    # 如果没有结构化信息，使用关键词匹配
    keywords1 = extract_keywords(address1)
    keywords2 = extract_keywords(address2)
    
    if not keywords1 or not keywords2:
        return False
    
    # 计算关键词重叠度
    common_keywords = set(keywords1) & set(keywords2)
    total_keywords = set(keywords1) | set(keywords2)
    
    if not total_keywords:
        return False
    
    similarity = len(common_keywords) / len(total_keywords)
    
    # 如果相似度大于50%，认为匹配
    return similarity > 0.5
