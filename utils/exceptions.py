# utils/exceptions.py


class TicketProcessingError(Exception):
    """通用工单处理错误基类"""
    pass


class QiyuAPIError(TicketProcessingError):
    """七鱼API调用错误"""
    pass


class PasswordResetAPIError(TicketProcessingError):
    """密码重置API调用错误"""
    pass


class AccountScraperError(TicketProcessingError):
    """账号信息爬取错误"""
    pass


class DataValidationError(TicketProcessingError):
    """数据格式或内容验证错误"""
    pass
